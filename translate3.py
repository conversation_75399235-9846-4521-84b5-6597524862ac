#!/usr/bin/env python3
"""
translate_pdf.py — v4 (OpenAI ≥ 1.0 + logging + PDF/TXT input)

Now accepts **either** a Russian PDF **or** a plain‑text (`.txt`) source file.

Usage (PDF):
    python translate_pdf.py --pdf book.pdf --style style.txt --output_txt out.txt

Usage (TXT):
    python translate_pdf.py --txt book_ru.txt --style style.txt --output_txt out.txt

All other flags (samples, log_dir, PDF output, etc.) work the same.

Dependencies:
    openai>=1  pdfplumber  tiktoken  tenacity  tqdm  reportlab
"""

from __future__ import annotations

import argparse
import json
import os
import shutil
import sys
import textwrap
from pathlib import Path
from typing import List, Tuple

import openai
from tenacity import retry, wait_exponential, stop_after_attempt

# Optional: pdfplumber only if we need PDF
try:
    import pdfplumber  # type: ignore
except ImportError:  # pragma: no cover
    pdfplumber = None  # type: ignore

# ---------------------------------------------------------------------------
# OpenAI client (>=1.0 API)
# ---------------------------------------------------------------------------

def get_client() -> openai.OpenAI:
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        sys.exit("Error: set OPENAI_API_KEY environment variable.")
    return openai.OpenAI(api_key=api_key)

CLIENT = get_client()

# ---------------------------------------------------------------------------
# Optional: tiktoken for accurate token counts (fallback to naive words)
# ---------------------------------------------------------------------------
try:
    import tiktoken

    _enc_cache: dict[str, tiktoken.Encoding] = {}

    def num_tokens(text: str, model: str = "gpt-4o-mini") -> int:  # type: ignore[name‑defined]
        if model not in _enc_cache:
            _enc_cache[model] = tiktoken.encoding_for_model(model)
        return len(_enc_cache[model].encode(text))

except ImportError:  # pragma: no cover – coarse fallback

    def num_tokens(text: str, model: str = "") -> int:  # type: ignore[unused‑argument]
        return len(text.split())

# ---------------------------------------------------------------------------
# Translation helper (with logging) -----------------------------------------
# ---------------------------------------------------------------------------

def _translate_chunk(
    chunk: str,
    style: str,
    examples: list[dict] | None,
    model: str,
    max_resp_tokens: int,
    temperature: float,
    log_prefix: Path | None,
) -> str:
    messages = [
        {
            "role": "system",
            "content": (
                "You are an assistant translating Russian text into idiomatic English. "
                "Reply ONLY with the English translation — no extra comments."
            ),
        },
        {"role": "system", "content": style},
    ]
    if examples:
        messages.extend(examples)
    messages.append({"role": "user", "content": chunk})

    payload = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
        "max_tokens": max_resp_tokens,
    }

    if log_prefix is not None:
        with open(log_prefix.with_suffix(".req.json"), "w", encoding="utf-8") as f:
            json.dump(payload, f, indent=2, ensure_ascii=False)

    resp = CLIENT.chat.completions.create(**payload)

    if log_prefix is not None:
        with open(log_prefix.with_suffix(".resp.json"), "w", encoding="utf-8") as f:
            json.dump(resp.model_dump(), f, indent=2, ensure_ascii=False)

    return resp.choices[0].message.content.strip()


@retry(wait=wait_exponential(multiplier=1, min=4, max=60), stop=stop_after_attempt(6))
def translate_chunk(*args, **kwargs):  # type: ignore[no‑any‑return]
    return _translate_chunk(*args, **kwargs)

# ---------------------------------------------------------------------------
# Chunking helpers ----------------------------------------------------------
# ---------------------------------------------------------------------------

def chunk_list(strings: List[str], model: str, token_limit: int) -> List[Tuple[List[int], str]]:
    """Generic: merge consecutive items into chunks under *token_limit*."""
    chunks: List[Tuple[List[int], str]] = []
    cur_idx: List[int] = []
    cur_txt = ""
    for i, s in enumerate(strings, 1):
        joined = f"{cur_txt}\n\n{s}" if cur_txt else s
        if num_tokens(joined, model) > token_limit and cur_txt:
            chunks.append((cur_idx, cur_txt))
            cur_idx = [i]
            cur_txt = s
        else:
            cur_idx.append(i)
            cur_txt = joined
    if cur_txt:
        chunks.append((cur_idx, cur_txt))
    return chunks


def read_source_pages(pdf_path: Path | None, txt_path: Path | None) -> List[str]:
    """Return list[str] representing logical pages/paragraphs."""
    if pdf_path:
        if pdfplumber is None:
            sys.exit("pdfplumber not installed; run: pip install pdfplumber")
        try:
            with pdfplumber.open(pdf_path) as pdf:
                return [page.extract_text() or "" for page in pdf.pages]
        except Exception as exc:
            sys.exit(f"Failed to read PDF: {exc}")
    else:  # TXT
        text = txt_path.read_text(encoding="utf-8")
        # Split on blank lines => paragraphs; fall back to wrap into 500‑word pages
        paras = [p.strip() for p in text.splitlines() if p.strip()]
        if not paras:
            paras = textwrap.wrap(text, 4000)  # fallback big chunk
        return paras

# ---------------------------------------------------------------------------
# PDF writer (unchanged) ----------------------------------------------------
# ---------------------------------------------------------------------------

def write_pdf(translations: List[Tuple[List[int], str]], pdf_path: Path) -> None:
    try:
        from reportlab.pdfgen import canvas as pdfcanvas
        from reportlab.lib.pagesizes import A4
        from reportlab.lib.units import mm
        from reportlab.lib.utils import simpleSplit
    except ImportError:
        sys.exit("reportlab is required for PDF output. Install with: pip install reportlab")

    c = pdfcanvas.Canvas(str(pdf_path), pagesize=A4)
    width, height = A4
    margin_x = 25 * mm
    margin_top = 25 * mm
    margin_bottom = 20 * mm
    usable_width = width - 2 * margin_x
    font_body = "Helvetica"
    font_header = "Helvetica-Bold"
    line_h = 12

    for idxs, eng_text in translations:
        label = f"BLOCK {idxs[0]}-{idxs[-1]}"
        c.setFont(font_header, 12)
        c.drawString(margin_x, height - margin_top, label)
        c.setFont(font_body, 11)
        y = height - margin_top - line_h * 2
        for raw_line in eng_text.split("\n"):
            for wline in simpleSplit(raw_line, font_body, 11, usable_width) or [" "]:
                if y < margin_bottom:
                    c.showPage()
                    c.setFont(font_body, 11)
                    y = height - margin_top
                c.drawString(margin_x, y, wline)
                y -= line_h
            y -= line_h * 0.3
        c.showPage()
    c.save()

# ---------------------------------------------------------------------------
# CLI ----------------------------------------------------------------------
# ---------------------------------------------------------------------------

def main() -> None:
    ap = argparse.ArgumentParser(
        description="Translate Russian PDF **or** TXT to English (with logging)",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    src = ap.add_mutually_exclusive_group(required=True)
    src.add_argument("--pdf", type=Path, help="Input Russian PDF file.")
    src.add_argument("--txt", type=Path, help="Input Russian plain‑text file (UTF‑8).")

    ap.add_argument("--style", required=True, type=Path, help="TXT with style / tone instructions.")
    ap.add_argument("--output_txt", required=True, type=Path, help="Plain‑text output file.")
    ap.add_argument("--output_pdf", type=Path, help="Optional PDF output file.")

    ap.add_argument("--sample_source", type=Path, help="Sample Russian excerpt TXT.")
    ap.add_argument("--sample_target", type=Path, help="Desired English translation of the sample excerpt TXT.")

    ap.add_argument("--model", default="gpt-4o-mini", help="OpenAI chat model name.")
    ap.add_argument("--chunk_tokens", type=int, default=3000, help="Prompt token budget per request.")
    ap.add_argument("--max_response_tokens", type=int, default=2048, help="Max tokens expected in response.")
    ap.add_argument("--temperature", type=float, default=0.2, help="Sampling temperature 0–1.")
    ap.add_argument("--log_dir", default="logs", help="Directory for per‑chunk traces; '' disables logs.")

    args = ap.parse_args()

    if (args.sample_source or args.sample_target) and not (args.sample_source and args.sample_target):
        ap.error("Provide BOTH --sample_source and --sample_target, or neither.")

    log_dir: Path | None
    if args.log_dir:
        log_dir = Path(args.log_dir)
        if log_dir.exists():
            shutil.rmtree(log_dir)
        log_dir.mkdir(parents=True, exist_ok=True)
    else:
        log_dir = None

    style_txt = args.style.read_text(encoding="utf-8").strip()

    # Sample messages
    samples: list[dict] | None = None
    if args.sample_source and args.sample_target:
        src_s = args.sample_source.read_text(encoding="utf-8").strip()
        trg_s = args.sample_target.read_text(encoding="utf-8").strip()
        samples = [
            {"role": "user", "content": f"SAMPLE SOURCE:\n{src_s}"},
            {"role": "assistant", "content": f"SAMPLE TARGET:\n{trg_s}"},
        ]

    # Read source and build chunks
    pages = read_source_pages(args.pdf, args.txt)
    chunks = chunk_list(pages, args.model, args.chunk_tokens)

    # Progress iterator
    try:
        from tqdm import tqdm
        iterator = tqdm(enumerate(chunks, 1), total=len(chunks), desc="Translating")
    except ImportError:
        iterator = enumerate(chunks, 1)

    translations: List[Tuple[List[int], str]] = []
    for n, (idxs, src_block) in iterator:
        if log_dir:
            (log_dir / f"chunk{n}.txt").write_text(src_block, encoding="utf-8")
            prefix = log_dir / f"chunk{n}"
        else:
            prefix = None
        eng = translate_chunk(
            src_block,
            style_txt,
            samples,
            args.model,
            args.max_response_tokens,
            args.temperature,
            log_prefix=prefix,
        )
        translations.append((idxs, eng))

    # Write outputs
    with args.output_txt.open("w", encoding="utf-8") as fh:
        for ids, eng in translations:
            fh.write(f"--- BLOCK {ids[0]}-{ids[-1]} ---\n{eng}\n\n")
    print(f"✔ TXT saved to {args.output_txt}")

    if args.output_pdf:
        write_pdf(translations, args.output_pdf)
        print(f"✔ PDF saved to {args.output_pdf}")
    if log_dir:
        print(f"✔ Debug logs saved in {log_dir}/")


if __name__ == "__main__":
    main()

