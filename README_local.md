# Local LLM Translation Script

This is a modified version of `translate3.py` that uses a local LLM running via LM Studio instead of the OpenAI API.

## Setup

1. **Install LM Studio** and load a model (e.g., <PERSON>wen, <PERSON>, or Mistral)
2. **Start the LM Studio server** (usually runs on `http://localhost:1234`)
3. **Install dependencies** (same as original script):
   ```bash
   pip install openai pdfplumber tiktoken tenacity tqdm reportlab
   ```

## Usage

The script works exactly like the original `translate3.py` but uses your local LLM:

### Translate a text file:
```bash
python3 translate3_local.py --txt input.txt --style style.txt --output_txt output.txt --sample_source sample_source.txt --sample_target sample_target.txt --model "qwen/qwen3-4b"
```

### Translate a PDF file:
```bash
python3 translate3_local.py --pdf input.pdf --style style.txt --output_txt output.txt --model "qwen/qwen3-4b"
```

## Configuration

### Environment Variables
- `LM_STUDIO_BASE_URL`: LM Studio endpoint (default: `http://localhost:1234/v1`)
- `LM_STUDIO_API_KEY`: API key (default: `lm-studio`, not required for local)

### Model Selection
Use `--model` to specify which model to use. Available models are shown when the script starts.
Common models in LM Studio:
- `qwen/qwen3-4b` (fast, good quality)
- `google/gemma-3-4b` (balanced)
- `mistralai/devstral-small-2505` (code-focused but works for translation)

## Key Differences from Original

1. **No OpenAI API key required** - Uses local LLM instead
2. **Slower processing** - Local models are typically slower than cloud APIs
3. **Post-processing** - Automatically extracts clean translations from verbose model outputs
4. **Model flexibility** - Can use any model loaded in LM Studio

## Performance Notes

- Local LLMs are typically slower than OpenAI API (30-60 seconds per chunk vs 1-5 seconds)
- Quality depends on the model used - larger models generally produce better translations
- For large files, consider using smaller chunk sizes (`--chunk_tokens 1000`) to reduce processing time per chunk

## Troubleshooting

1. **Connection errors**: Make sure LM Studio is running and has a model loaded
2. **Slow processing**: Try a smaller model or reduce `--chunk_tokens`
3. **Poor translation quality**: Try a larger model or adjust the temperature (`--temperature 0.1`)
4. **Verbose output**: The script automatically filters out model commentary, but some may still appear

## Example Test

Test with the provided small sample:
```bash
python3 translate3_local.py --txt test_small.txt --style style.txt --output_txt test_output.txt --sample_source sample_source.txt --sample_target sample_target.txt --model "qwen/qwen3-4b" --chunk_tokens 1000
```

This should produce a clean English translation following the Roman gladiator style guide.
