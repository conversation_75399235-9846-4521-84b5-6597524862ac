#!/usr/bin/env python3
"""
improve_samples.py - Interactive translation sample improvement tool

This script helps you build and refine translation examples by:
1. Picking random sentences from the source text
2. Translating them using the current llama.cpp setup
3. Allowing you to approve or provide alternative translations
4. Adding approved pairs to sample_source.txt and sample_target.txt

This builds a curated translation memory that improves consistency and style.

Usage:
    python3 improve_samples.py --source in.txt --style style.txt [options]

Prerequisites:
    - llama.cpp server running (use ./startLLM.sh)
    - translate3_llamacpp.py working correctly
"""

import argparse
import random
import sys
import textwrap
from pathlib import Path
from typing import List, Tuple
import re

# Import translation functionality from our existing script
try:
    from translate3_llamacpp import get_client, _translate_chunk, condense_style_guide
except ImportError:
    print("❌ Error: Could not import translation functions from translate3_llamacpp.py")
    print("Make sure translate3_llamacpp.py is in the same directory.")
    sys.exit(1)

# Colors for better CLI experience
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    CYAN = '\033[96m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

def print_colored(text: str, color: str = Colors.END) -> None:
    """Print text with color."""
    print(f"{color}{text}{Colors.END}")

def extract_sentences(text: str) -> List[str]:
    """Extract sentences from text, filtering out very short or very long ones."""
    # Split on sentence endings, but be careful with abbreviations
    sentences = re.split(r'[.!?]+\s+', text)
    
    # Clean and filter sentences
    cleaned_sentences = []
    for sentence in sentences:
        sentence = sentence.strip()
        if not sentence:
            continue
            
        # Skip very short sentences (likely fragments)
        if len(sentence.split()) < 5:
            continue
            
        # Skip very long sentences (likely paragraphs)
        if len(sentence.split()) > 50:
            continue
            
        # Skip sentences that are mostly punctuation or numbers
        if len(re.sub(r'[^\w\s]', '', sentence)) < 10:
            continue
            
        cleaned_sentences.append(sentence)
    
    return cleaned_sentences

def get_random_sentence(source_file: Path, existing_sources: set) -> str:
    """Get a random sentence from the source file that hasn't been used yet."""
    text = source_file.read_text(encoding='utf-8')
    sentences = extract_sentences(text)

    # Filter out sentences we've already used
    unused_sentences = [s for s in sentences if s not in existing_sources]

    if not unused_sentences:
        print_colored("⚠️  All sentences have been processed! Consider using a larger source file.", Colors.YELLOW)
        return random.choice(sentences)  # Fallback to any sentence

    return random.choice(unused_sentences)

def load_existing_samples(sample_source_file: Path, sample_target_file: Path) -> tuple[set, list[dict]]:
    """Load existing sample sources to avoid duplicates and prepare examples for translation."""
    sources = set()
    examples = []

    if not sample_source_file.exists() or not sample_target_file.exists():
        return sources, examples

    source_content = sample_source_file.read_text(encoding='utf-8')
    target_content = sample_target_file.read_text(encoding='utf-8')

    # Parse source and target files to extract pairs
    source_texts = []
    target_texts = []

    # Extract source texts (skip delimiter lines)
    for line in source_content.split('\n'):
        line = line.strip()
        if line and not line.startswith('---') and not line.startswith('SAMPLE SOURCE:'):
            source_texts.append(line)
            sources.add(line)

    # Extract target texts (skip delimiter lines)
    for line in target_content.split('\n'):
        line = line.strip()
        if line and not line.startswith('---') and not line.startswith('SAMPLE TARGET:'):
            target_texts.append(line)

    # Create examples in the format expected by the translation function
    # Use the last few pairs as examples (limit to avoid context overflow)
    max_examples = 3  # Use last 3 pairs as examples
    for i in range(max(0, len(source_texts) - max_examples), len(source_texts)):
        if i < len(target_texts):
            examples.extend([
                {"role": "user", "content": f"SAMPLE SOURCE:\n{source_texts[i]}"},
                {"role": "assistant", "content": f"SAMPLE TARGET:\n{target_texts[i]}"}
            ])

    return sources, examples

def translate_sentence(sentence: str, style: str, examples: list[dict], model: str = "gemma-2-27b-it") -> str:
    """Translate a single sentence using our existing translation logic with examples."""
    try:
        # Use the same translation function as our main script, WITH examples for consistency
        translation = _translate_chunk(
            chunk=sentence,
            style=style,
            examples=examples,  # Use existing examples for better consistency
            model=model,
            max_resp_tokens=512,
            temperature=0.3,
            log_prefix=None
        )
        return translation.strip()
    except Exception as e:
        print_colored(f"❌ Translation error: {e}", Colors.RED)
        return "[Translation failed]"

def display_translation_pair(source: str, translation: str) -> None:
    """Display the source and translation in a nice format."""
    print("\n" + "="*80)
    print_colored("📖 ORIGINAL (Russian):", Colors.BLUE)
    print(textwrap.fill(source, width=76, initial_indent="  ", subsequent_indent="  "))
    print()
    print_colored("🔄 AI TRANSLATION (English):", Colors.CYAN)
    print(textwrap.fill(translation, width=76, initial_indent="  ", subsequent_indent="  "))
    print("="*80)

def get_user_choice() -> str:
    """Get user's choice: approve, edit, or skip."""
    while True:
        print_colored("\nWhat would you like to do?", Colors.YELLOW)
        print("  [a] Approve (translation is good, current guidelines sufficient)")
        print("  [e] Edit/provide alternative (add to guidelines)")
        print("  [s] Skip this sentence")
        print("  [q] Quit")

        choice = input("\nYour choice (a/e/s/q): ").lower().strip()

        if choice in ['a', 'e', 's', 'q']:
            return choice

        print_colored("Invalid choice. Please enter 'a', 'e', 's', or 'q'.", Colors.RED)

def get_alternative_translation() -> str:
    """Get alternative translation from user."""
    print_colored("\n✏️  Please provide your preferred translation:", Colors.GREEN)
    print("(Press Enter on empty line to finish, or type 'cancel' to cancel)")
    
    lines = []
    while True:
        line = input("  ")
        if line.lower() == 'cancel':
            return ""
        if not line:
            break
        lines.append(line)
    
    return ' '.join(lines).strip()

def append_sample_pair(source: str, target: str, sample_source_file: Path, sample_target_file: Path) -> None:
    """Append a new sample pair to the sample files using simple delimiters."""
    # Append to sample_source.txt with simple delimiter
    with sample_source_file.open('a', encoding='utf-8') as f:
        f.write(f"\n---\n{source}\n")

    # Append to sample_target.txt with simple delimiter
    with sample_target_file.open('a', encoding='utf-8') as f:
        f.write(f"\n---\n{target}\n")

def main():
    parser = argparse.ArgumentParser(
        description="Interactive tool to improve translation samples",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument("--source", type=Path, default="in.txt", 
                       help="Source Russian text file")
    parser.add_argument("--style", type=Path, default="style.txt",
                       help="Style guide file")
    parser.add_argument("--sample_source", type=Path, default="sample_source.txt",
                       help="Sample source file to append to")
    parser.add_argument("--sample_target", type=Path, default="sample_target.txt", 
                       help="Sample target file to append to")
    parser.add_argument("--model", default="gemma-2-27b-it",
                       help="Model name for translation")
    parser.add_argument("--max_iterations", type=int, default=50,
                       help="Maximum number of sentences to process in one session")
    
    args = parser.parse_args()
    
    # Validate files
    if not args.source.exists():
        print_colored(f"❌ Source file not found: {args.source}", Colors.RED)
        sys.exit(1)
    
    if not args.style.exists():
        print_colored(f"❌ Style file not found: {args.style}", Colors.RED)
        sys.exit(1)
    
    # Test connection to llama.cpp server
    try:
        client = get_client()
        models = client.models.list()
        print_colored(f"✅ Connected to llama.cpp server. Available models: {[m.id for m in models.data]}", Colors.GREEN)
    except Exception as e:
        print_colored(f"❌ Cannot connect to llama.cpp server: {e}", Colors.RED)
        print_colored("Make sure to start the server with: ./startLLM.sh", Colors.YELLOW)
        sys.exit(1)
    
    # Load style guide
    style_text = args.style.read_text(encoding='utf-8').strip()
    
    # Load existing samples to avoid duplicates and prepare examples
    existing_sources, examples = load_existing_samples(args.sample_source, args.sample_target)
    
    print_colored("🎯 Translation Sample Improvement Tool", Colors.HEADER + Colors.BOLD)
    print_colored("=" * 50, Colors.HEADER)
    print(f"Source file: {args.source}")
    print(f"Existing samples: {len(existing_sources)}")
    print(f"Max iterations: {args.max_iterations}")
    print()
    
    approved_count = 0
    edited_count = 0
    skipped_count = 0
    
    for iteration in range(args.max_iterations):
        print_colored(f"\n🔄 Iteration {iteration + 1}/{args.max_iterations}", Colors.HEADER)
        
        # Get random sentence
        try:
            sentence = get_random_sentence(args.source, existing_sources)
        except Exception as e:
            print_colored(f"❌ Error getting sentence: {e}", Colors.RED)
            continue
        
        # Translate it using existing examples for consistency
        print_colored("🤖 Translating with current guidelines...", Colors.YELLOW)
        translation = translate_sentence(sentence, style_text, examples, args.model)
        
        # Display the pair
        display_translation_pair(sentence, translation)
        
        # Get user choice
        choice = get_user_choice()
        
        if choice == 'q':
            print_colored("\n👋 Goodbye!", Colors.GREEN)
            break
        elif choice == 's':
            print_colored("⏭️  Skipped.", Colors.YELLOW)
            skipped_count += 1
            continue
        elif choice == 'a':
            # Approve the translation - don't add to samples since current guidelines are sufficient
            existing_sources.add(sentence)  # Track as processed but don't save
            approved_count += 1
            print_colored("✅ Translation approved! Current guidelines are working well.", Colors.GREEN)
        elif choice == 'e':
            # Get alternative translation and add to guidelines
            alternative = get_alternative_translation()
            if alternative:
                append_sample_pair(sentence, alternative, args.sample_source, args.sample_target)
                existing_sources.add(sentence)
                # Update examples for next iterations in this session
                examples.extend([
                    {"role": "user", "content": f"SAMPLE SOURCE:\n{sentence}"},
                    {"role": "assistant", "content": f"SAMPLE TARGET:\n{alternative}"}
                ])
                # Keep only recent examples to avoid context overflow
                if len(examples) > 6:  # Keep last 3 pairs (6 messages)
                    examples = examples[-6:]
                edited_count += 1
                print_colored("✅ Your translation added to guidelines!", Colors.GREEN)
            else:
                print_colored("❌ Cancelled.", Colors.YELLOW)
                continue
    
    # Summary
    print_colored(f"\n📊 SESSION SUMMARY", Colors.HEADER + Colors.BOLD)
    print_colored("=" * 30, Colors.HEADER)
    print(f"✅ Approved: {approved_count}")
    print(f"✏️  Edited: {edited_count}")
    print(f"⏭️  Skipped: {skipped_count}")
    print(f"📝 New guidelines added: {edited_count}")
    print(f"📚 Total guidelines in database: {len(existing_sources) + edited_count}")

    if edited_count > 0:
        print_colored(f"\n🎉 Great work! Your translation guidelines have been improved.", Colors.GREEN)
        print_colored(f"These will help maintain consistency in future translations.", Colors.GREEN)
    elif approved_count > 0:
        print_colored(f"\n✅ Good news! Your current guidelines are working well.", Colors.GREEN)
        print_colored(f"The AI translations matched your expectations.", Colors.GREEN)

if __name__ == "__main__":
    main()
