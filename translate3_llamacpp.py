#!/usr/bin/env python3
"""
translate3_llamacpp.py — llama.cpp version with parallel processing

Modified version of translate3.py that uses llama.cpp server instead of OpenAI API.
llama.cpp provides OpenAI-compatible endpoints when run in server mode:
- GET /v1/models
- POST /v1/chat/completions
- POST /v1/completions
- POST /v1/embeddings

Usage (PDF):
    python translate3_llamacpp.py --pdf book.pdf --style style.txt --output_txt out.txt

Usage (TXT):
    python translate3_llamacpp.py --txt book_ru.txt --style style.txt --output_txt out.txt

Prerequisites:
1. Build llama.cpp: 
   git clone https://github.com/ggerganov/llama.cpp.git
   cd llama.cpp && mkdir build && cd build && cmake .. && make -j4

2. Download a GGUF model (e.g., Gemma 2B):
   curl -L -o gemma-2-2b-it-Q4_K_M.gguf https://huggingface.co/bartowski/gemma-2-2b-it-GGUF/resolve/main/gemma-2-2b-it-Q4_K_M.gguf

3. Start llama.cpp server:
   ./build/bin/llama-server --model gemma-2-2b-it-Q4_K_M.gguf --port 8080 --host 0.0.0.0 --ctx-size 4096

Dependencies:
    openai>=1  pdfplumber  tiktoken  tenacity  tqdm  reportlab
"""

from __future__ import annotations

import argparse
import json
import os
import shutil
import sys
import textwrap
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
from typing import List, Tuple
import threading

import openai
from tenacity import retry, wait_exponential, stop_after_attempt

# Optional: pdfplumber only if we need PDF
try:
    import pdfplumber  # type: ignore
except ImportError:  # pragma: no cover
    pdfplumber = None  # type: ignore

# ---------------------------------------------------------------------------
# llama.cpp client (OpenAI-compatible API)
# ---------------------------------------------------------------------------

def get_client() -> openai.OpenAI:
    """
    Create OpenAI client configured for llama.cpp server.
    llama.cpp server typically runs on http://localhost:8080 with OpenAI-compatible endpoints.
    """
    # Default llama.cpp server endpoint
    base_url = os.getenv("LLAMACPP_BASE_URL", "http://localhost:8080/v1")
    
    # llama.cpp doesn't require an API key, but the OpenAI client expects one
    # We can use a dummy key since llama.cpp ignores it
    api_key = os.getenv("LLAMACPP_API_KEY", "llamacpp")
    
    print(f"Connecting to llama.cpp server at: {base_url}")
    
    return openai.OpenAI(
        api_key=api_key,
        base_url=base_url
    )

# Thread-local storage for clients to avoid sharing across threads
_thread_local = threading.local()

def get_thread_client() -> openai.OpenAI:
    """Get a thread-local OpenAI client for parallel processing."""
    if not hasattr(_thread_local, 'client'):
        base_url = os.getenv("LLAMACPP_BASE_URL", "http://localhost:8080/v1")
        api_key = os.getenv("LLAMACPP_API_KEY", "llamacpp")
        _thread_local.client = openai.OpenAI(
            api_key=api_key,
            base_url=base_url
        )
    return _thread_local.client

CLIENT = get_client()

# ---------------------------------------------------------------------------
# Optional: tiktoken for accurate token counts (fallback to naive words)
# ---------------------------------------------------------------------------
try:
    import tiktoken

    _enc_cache: dict[str, tiktoken.Encoding] = {}

    def num_tokens(text: str, model: str = "gpt-4o-mini") -> int:  # type: ignore[name‑defined]
        # For local models, we might not have tiktoken support
        # Fall back to word-based estimation
        try:
            if model not in _enc_cache:
                _enc_cache[model] = tiktoken.encoding_for_model(model)
            return len(_enc_cache[model].encode(text))
        except Exception:
            # Fallback to word count if tiktoken doesn't support the local model
            return len(text.split())

except ImportError:  # pragma: no cover – coarse fallback

    def num_tokens(text: str, model: str = "") -> int:  # type: ignore[unused‑argument]
        return len(text.split())

# ---------------------------------------------------------------------------
# Translation helper (with logging) -----------------------------------------
# ---------------------------------------------------------------------------

def _translate_chunk(
    chunk: str,
    style: str,
    examples: list[dict] | None,
    model: str,
    max_resp_tokens: int,
    temperature: float,
    log_prefix: Path | None,
) -> str:
    # Check if this is a T5-based model (like MADLAD-400) that needs special formatting
    if "madlad" in model.lower() or "t5" in model.lower():
        # T5 models expect direct text input with task prefix
        prompt = f"translate Russian to English: {chunk}"
        payload = {
            "model": model,
            "prompt": prompt,
            "temperature": temperature,
            "max_tokens": max_resp_tokens,
        }

        if log_prefix is not None:
            with open(log_prefix.with_suffix(".req.json"), "w", encoding="utf-8") as f:
                json.dump(payload, f, indent=2, ensure_ascii=False)

        try:
            # Use thread-local client for parallel processing
            client = get_thread_client()
            resp = client.completions.create(**payload)
        except Exception as e:
            print(f"Error calling llama.cpp server: {e}")
            print("Make sure llama.cpp server is running and accessible.")
            raise

        if log_prefix is not None:
            with open(log_prefix.with_suffix(".resp.json"), "w", encoding="utf-8") as f:
                json.dump(resp.model_dump(), f, indent=2, ensure_ascii=False)

        content = resp.choices[0].text.strip()
        return content
    else:
        # Regular chat-based models
        messages = [
            {
                "role": "system",
                "content": (
                    "TASK: Translate Russian text to English. "
                    "CRITICAL: The user will provide Russian text in Cyrillic script. You must translate it to English. "
                    "DO NOT provide writing advice, critique, or commentary. "
                    "DO NOT treat the text as if it's already in English. "
                    "ONLY output the English translation of the Russian text."
                ),
            },
            {"role": "system", "content": f"Translation style guide:\n{style}"},
        ]
        if examples:
            messages.extend(examples)
        messages.append({"role": "user", "content": f"Translate this Russian text to English:\n\n{chunk}"})

        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_resp_tokens,
        }

        if log_prefix is not None:
            with open(log_prefix.with_suffix(".req.json"), "w", encoding="utf-8") as f:
                json.dump(payload, f, indent=2, ensure_ascii=False)

        try:
            # Use thread-local client for parallel processing
            client = get_thread_client()
            resp = client.chat.completions.create(**payload)
        except Exception as e:
            print(f"Error calling llama.cpp server: {e}")
            print("Make sure llama.cpp server is running and accessible.")
            raise

        if log_prefix is not None:
            with open(log_prefix.with_suffix(".resp.json"), "w", encoding="utf-8") as f:
                json.dump(resp.model_dump(), f, indent=2, ensure_ascii=False)

        content = resp.choices[0].message.content.strip()

        # Post-process to extract just the translation if the model adds extra content
        # Look for content after **Translation:** marker

        # First, try to find content after **Translation:** marker
        if "**Translation:**" in content:
            parts = content.split("**Translation:**", 1)
            if len(parts) > 1:
                translation_part = parts[1]
                # Remove everything after --- or **Key Notes:** or similar markers
                for end_marker in ["---", "**Key Notes:**", "**Notes:**", "Let me know"]:
                    if end_marker in translation_part:
                        translation_part = translation_part.split(end_marker)[0]
                return translation_part.strip()

        # If no **Translation:** marker, look for content that starts with quotes or dialogue
        lines = content.split('\n')
        translation_lines = []
        skip_thinking = True

        for line in lines:
            line = line.strip()
            if not line:
                if not skip_thinking:
                    translation_lines.append('')
                continue

            # Skip thinking blocks
            if line.startswith('<think>'):
                skip_thinking = True
                continue
            if line.startswith('</think>'):
                skip_thinking = False
                continue
            if skip_thinking:
                continue

            # Stop at notes or commentary sections
            if any(marker in line for marker in ["**Key Notes:**", "**Notes:**", "---", "Let me know"]):
                break

            # Include lines that look like translation content
            if (line.startswith('"') or line.startswith('—') or line.startswith('-') or
                any(word in line.lower()[:50] for word in ['morning', 'men', 'stood', 'asked', 'called', 'said', 'twenty', 'freckles'])):
                translation_lines.append(line)
            elif translation_lines:  # Continue adding lines if we've started collecting translation
                translation_lines.append(line)

        if translation_lines:
            return '\n'.join(translation_lines).strip()

        # Last resort: return the original content
        return content


@retry(wait=wait_exponential(multiplier=1, min=4, max=60), stop=stop_after_attempt(6))
def translate_chunk(*args, **kwargs):  # type: ignore[no‑any‑return]
    return _translate_chunk(*args, **kwargs)

# ---------------------------------------------------------------------------
# Chunking helpers ----------------------------------------------------------
# ---------------------------------------------------------------------------

def split_large_paragraph(text: str, model: str, max_tokens: int) -> List[str]:
    """Split a large paragraph into smaller pieces if it exceeds max_tokens."""
    if num_tokens(text, model) <= max_tokens:
        return [text]
    
    # Try splitting by sentences first
    sentences = []
    current = ""
    
    # Simple sentence splitting on periods, exclamation marks, question marks
    for char in text:
        current += char
        if char in '.!?' and len(current.strip()) > 10:
            sentences.append(current.strip())
            current = ""
    
    if current.strip():
        sentences.append(current.strip())
    
    # If we have sentences, group them into chunks
    if len(sentences) > 1:
        chunks = []
        current_chunk = ""
        
        for sentence in sentences:
            test_chunk = f"{current_chunk} {sentence}".strip()
            if num_tokens(test_chunk, model) > max_tokens and current_chunk:
                chunks.append(current_chunk)
                current_chunk = sentence
            else:
                current_chunk = test_chunk
        
        if current_chunk:
            chunks.append(current_chunk)
        
        return chunks
    
    # If sentence splitting didn't help, split by words
    words = text.split()
    if len(words) <= 1:
        return [text]  # Can't split further
    
    chunks = []
    current_chunk = ""
    
    for word in words:
        test_chunk = f"{current_chunk} {word}".strip()
        if num_tokens(test_chunk, model) > max_tokens and current_chunk:
            chunks.append(current_chunk)
            current_chunk = word
        else:
            current_chunk = test_chunk
    
    if current_chunk:
        chunks.append(current_chunk)
    
    return chunks


def chunk_list(strings: List[str], model: str, token_limit: int) -> List[Tuple[List[int], str]]:
    """Generic: merge consecutive items into chunks under *token_limit*."""
    # First, split any overly large paragraphs
    processed_strings = []
    for s in strings:
        if num_tokens(s, model) > token_limit * 0.8:  # Split if close to limit
            sub_chunks = split_large_paragraph(s, model, int(token_limit * 0.7))
            processed_strings.extend(sub_chunks)
        else:
            processed_strings.append(s)
    
    # Now chunk the processed strings
    chunks: List[Tuple[List[int], str]] = []
    cur_idx: List[int] = []
    cur_txt = ""
    
    for i, s in enumerate(processed_strings, 1):
        joined = f"{cur_txt}\n\n{s}" if cur_txt else s
        if num_tokens(joined, model) > token_limit and cur_txt:
            chunks.append((cur_idx, cur_txt))
            cur_idx = [i]
            cur_txt = s
        else:
            cur_idx.append(i)
            cur_txt = joined
    
    if cur_txt:
        chunks.append((cur_idx, cur_txt))
    
    return chunks


def read_source_pages(pdf_path: Path | None, txt_path: Path | None) -> List[str]:
    """Return list[str] representing logical pages/paragraphs."""
    if pdf_path:
        if pdfplumber is None:
            sys.exit("pdfplumber not installed; run: pip install pdfplumber")
        try:
            with pdfplumber.open(pdf_path) as pdf:
                return [page.extract_text() or "" for page in pdf.pages]
        except Exception as exc:
            sys.exit(f"Failed to read PDF: {exc}")
    else:  # TXT
        text = txt_path.read_text(encoding="utf-8")
        # Split on blank lines => paragraphs; fall back to wrap into 500‑word pages
        paras = [p.strip() for p in text.splitlines() if p.strip()]
        if not paras:
            paras = textwrap.wrap(text, 4000)  # fallback big chunk
        return paras

# ---------------------------------------------------------------------------
# PDF writer (unchanged) ----------------------------------------------------
# ---------------------------------------------------------------------------

def write_pdf(translations: List[Tuple[List[int], str]], pdf_path: Path) -> None:
    try:
        from reportlab.pdfgen import canvas as pdfcanvas
        from reportlab.lib.pagesizes import A4
        from reportlab.lib.units import mm
        from reportlab.lib.utils import simpleSplit
    except ImportError:
        sys.exit("reportlab is required for PDF output. Install with: pip install reportlab")

    c = pdfcanvas.Canvas(str(pdf_path), pagesize=A4)
    width, height = A4
    margin_x = 25 * mm
    margin_top = 25 * mm
    margin_bottom = 20 * mm
    usable_width = width - 2 * margin_x
    font_body = "Helvetica"
    font_header = "Helvetica-Bold"
    line_h = 12

    for idxs, eng_text in translations:
        label = f"BLOCK {idxs[0]}-{idxs[-1]}"
        c.setFont(font_header, 12)
        c.drawString(margin_x, height - margin_top, label)
        c.setFont(font_body, 11)
        y = height - margin_top - line_h * 2
        for raw_line in eng_text.split("\n"):
            for wline in simpleSplit(raw_line, font_body, 11, usable_width) or [" "]:
                if y < margin_bottom:
                    c.showPage()
                    c.setFont(font_body, 11)
                    y = height - margin_top
                c.drawString(margin_x, y, wline)
                y -= line_h
            y -= line_h * 0.3
        c.showPage()
    c.save()

# ---------------------------------------------------------------------------
# CLI ----------------------------------------------------------------------
# ---------------------------------------------------------------------------

def main() -> None:
    ap = argparse.ArgumentParser(
        description="Translate Russian PDF **or** TXT to English using llama.cpp server",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    src = ap.add_mutually_exclusive_group(required=True)
    src.add_argument("--pdf", type=Path, help="Input Russian PDF file.")
    src.add_argument("--txt", type=Path, help="Input Russian plain‑text file (UTF‑8).")

    ap.add_argument("--style", required=True, type=Path, help="TXT with style / tone instructions.")
    ap.add_argument("--output_txt", required=True, type=Path, help="Plain‑text output file.")
    ap.add_argument("--output_pdf", type=Path, help="Optional PDF output file.")

    ap.add_argument("--sample_source", type=Path, help="Sample Russian excerpt TXT.")
    ap.add_argument("--sample_target", type=Path, help="Desired English translation of the sample excerpt TXT.")

    # Default model name - user should specify the model name as it appears in llama.cpp
    ap.add_argument("--model", default="gemma-2-2b-it", help="Model name (as configured in llama.cpp server).")
    ap.add_argument("--chunk_tokens", type=int, default=1500, help="Prompt token budget per request (smaller for parallel processing).")
    ap.add_argument("--max_response_tokens", type=int, default=2048, help="Max tokens expected in response.")
    ap.add_argument("--temperature", type=float, default=0.2, help="Sampling temperature 0–1.")
    ap.add_argument("--log_dir", default="logs_llamacpp", help="Directory for per‑chunk traces; '' disables logs.")
    ap.add_argument("--workers", type=int, default=2, help="Number of parallel workers for translation (1-8 recommended).")

    args = ap.parse_args()

    if (args.sample_source or args.sample_target) and not (args.sample_source and args.sample_target):
        ap.error("Provide BOTH --sample_source and --sample_target, or neither.")

    # Validate workers parameter
    if args.workers < 1 or args.workers > 16:
        ap.error("Number of workers must be between 1 and 16.")

    # Test connection to llama.cpp server
    try:
        models = CLIENT.models.list()
        print(f"Available models: {[model.id for model in models.data]}")
        if not models.data:
            print("Warning: No models found. Make sure llama.cpp server is running with a model loaded.")
    except Exception as e:
        print(f"Failed to connect to llama.cpp server: {e}")
        print("Make sure llama.cpp server is running on the expected endpoint.")
        print("Start server with: ./build/bin/llama-server --model your-model.gguf --port 8080")
        sys.exit(1)

    log_dir: Path | None
    if args.log_dir:
        log_dir = Path(args.log_dir)
        if log_dir.exists():
            shutil.rmtree(log_dir)
        log_dir.mkdir(parents=True, exist_ok=True)
    else:
        log_dir = None

    style_txt = args.style.read_text(encoding="utf-8").strip()

    # Sample messages
    samples: list[dict] | None = None
    if args.sample_source and args.sample_target:
        src_s = args.sample_source.read_text(encoding="utf-8").strip()
        trg_s = args.sample_target.read_text(encoding="utf-8").strip()
        samples = [
            {"role": "user", "content": f"SAMPLE SOURCE:\n{src_s}"},
            {"role": "assistant", "content": f"SAMPLE TARGET:\n{trg_s}"},
        ]

    # Read source and build chunks
    pages = read_source_pages(args.pdf, args.txt)
    chunks = chunk_list(pages, args.model, args.chunk_tokens)

    print(f"Processing {len(chunks)} chunks with {args.workers} parallel workers...")

    def translate_single_chunk(chunk_data):
        """Translate a single chunk - used for parallel processing."""
        n, (idxs, src_block) = chunk_data

        if log_dir:
            (log_dir / f"chunk{n}.txt").write_text(src_block, encoding="utf-8")
            prefix = log_dir / f"chunk{n}"
        else:
            prefix = None

        eng = translate_chunk(
            src_block,
            style_txt,
            samples,
            args.model,
            args.max_response_tokens,
            args.temperature,
            log_prefix=prefix,
        )
        return (n, idxs, eng)

    # Process chunks in parallel
    translations: List[Tuple[List[int], str]] = []

    if args.workers == 1:
        # Sequential processing for single worker
        try:
            from tqdm import tqdm
            iterator = tqdm(enumerate(chunks, 1), total=len(chunks), desc="Translating")
        except ImportError:
            iterator = enumerate(chunks, 1)

        for chunk_data in iterator:
            n, idxs, eng = translate_single_chunk(chunk_data)
            translations.append((idxs, eng))
    else:
        # Parallel processing for multiple workers
        chunk_data_list = list(enumerate(chunks, 1))
        completed_translations = {}

        try:
            from tqdm import tqdm
            progress_bar = tqdm(total=len(chunks), desc="Translating")
        except ImportError:
            progress_bar = None

        with ThreadPoolExecutor(max_workers=args.workers) as executor:
            # Submit all chunks for processing
            future_to_chunk = {
                executor.submit(translate_single_chunk, chunk_data): chunk_data[0]
                for chunk_data in chunk_data_list
            }

            # Collect results as they complete
            for future in as_completed(future_to_chunk):
                try:
                    n, idxs, eng = future.result()
                    completed_translations[n] = (idxs, eng)
                    if progress_bar:
                        progress_bar.update(1)
                except Exception as e:
                    chunk_num = future_to_chunk[future]
                    print(f"Error processing chunk {chunk_num}: {e}")
                    raise

        if progress_bar:
            progress_bar.close()

        # Sort translations by chunk number to maintain order
        for n in sorted(completed_translations.keys()):
            translations.append(completed_translations[n])

    # Write outputs
    with args.output_txt.open("w", encoding="utf-8") as fh:
        for ids, eng in translations:
            fh.write(f"--- BLOCK {ids[0]}-{ids[-1]} ---\n{eng}\n\n")
    print(f"✔ TXT saved to {args.output_txt}")

    if args.output_pdf:
        write_pdf(translations, args.output_pdf)
        print(f"✔ PDF saved to {args.output_pdf}")
    if log_dir:
        print(f"✔ Debug logs saved in {log_dir}/")


if __name__ == "__main__":
    main()
