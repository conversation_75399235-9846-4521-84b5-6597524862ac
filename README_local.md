# Local LLM Translation Script with Parallel Processing

This is a modified version of `translate3.py` that uses a local LLM running via LM Studio instead of the OpenAI API, with added parallel processing capabilities for faster translation.

## New Features

- ✅ **Parallel Processing**: Process multiple chunks simultaneously using multiple workers
- ✅ **Smart Chunking**: Automatically splits large paragraphs into smaller, manageable pieces
- ✅ **Configurable Concurrency**: Control the number of parallel workers (1-16)
- ✅ **Improved Performance**: Significantly faster translation for large documents

## Setup

1. **Install LM Studio** and load a model (e.g., <PERSON><PERSON>, <PERSON>, or <PERSON>stra<PERSON>)
2. **Start the LM Studio server** (usually runs on `http://localhost:1234`)
3. **Install dependencies** (same as original script):
   ```bash
   pip install openai pdfplumber tiktoken tenacity tqdm reportlab
   ```

## Usage

The script works exactly like the original `translate3.py` but with additional parallel processing options:

### Translate a text file with parallel processing:
```bash
python3 translate3_local.py --txt input.txt --style style.txt --output_txt output.txt --sample_source sample_source.txt --sample_target sample_target.txt --model "google/gemma-3-27b" --workers 3
```

### Translate a PDF file:
```bash
python3 translate3_local.py --pdf input.pdf --style style.txt --output_txt output.txt --model "google/gemma-3-27b" --workers 4
```

## Configuration

### Environment Variables
- `LM_STUDIO_BASE_URL`: LM Studio endpoint (default: `http://localhost:1234/v1`)
- `LM_STUDIO_API_KEY`: API key (default: `lm-studio`, not required for local)

### Model Selection
Use `--model` to specify which model to use. Available models are shown when the script starts.
Common models in LM Studio:
- `google/gemma-3-27b` (recommended, high quality, default)
- `qwen/qwen3-4b` (fast, good quality)
- `google/gemma-3-4b` (balanced)
- `mistralai/devstral-small-2505` (code-focused but works for translation)

### Parallel Processing Options
- `--workers N`: Number of parallel workers (default: 3, range: 1-16)
- `--chunk_tokens N`: Token limit per chunk (default: 2000, smaller for better parallelization)

**Recommended settings:**
- For fast processing: `--workers 4 --chunk_tokens 1500`
- For quality: `--workers 2 --chunk_tokens 2500`
- For testing: `--workers 1` (sequential processing)

## Key Differences from Original

1. **No OpenAI API key required** - Uses local LLM instead
2. **Parallel processing** - Process multiple chunks simultaneously for faster translation
3. **Smart chunking** - Automatically splits large paragraphs for better parallelization
4. **Post-processing** - Automatically extracts clean translations from verbose model outputs
5. **Model flexibility** - Can use any model loaded in LM Studio

## Performance Notes

- **Parallel processing significantly improves speed** - 3 workers can reduce translation time by ~60%
- Local LLMs are typically slower than OpenAI API per chunk, but parallelization helps
- Quality depends on the model used - larger models generally produce better translations
- **Optimal settings for speed**: `--workers 3-4 --chunk_tokens 1500-2000`
- **Memory usage**: Each worker uses additional memory; reduce workers if you encounter issues

## Troubleshooting

1. **Connection errors**: Make sure LM Studio is running and has a model loaded
2. **Slow processing**: Try a smaller model or reduce `--chunk_tokens`
3. **Poor translation quality**: Try a larger model or adjust the temperature (`--temperature 0.1`)
4. **Verbose output**: The script automatically filters out model commentary, but some may still appear

## Example Tests

### Test with small sample (single chunk):
```bash
python3 translate3_local.py --txt test_small.txt --style style.txt --output_txt test_output.txt --sample_source sample_source.txt --sample_target sample_target.txt --model "google/gemma-3-27b" --workers 2
```

### Test with parallel processing (multiple chunks):
```bash
python3 translate3_local.py --txt test_medium.txt --style style.txt --output_txt test_parallel.txt --sample_source sample_source.txt --sample_target sample_target.txt --model "google/gemma-3-27b" --workers 3 --chunk_tokens 800
```

### Full document translation:
```bash
python3 translate3_local.py --txt in.txt --style style.txt --output_txt out_parallel.txt --sample_source sample_source.txt --sample_target sample_target.txt --model "google/gemma-3-27b" --workers 4 --chunk_tokens 1500
```

These should produce clean English translations following the Roman gladiator style guide.
